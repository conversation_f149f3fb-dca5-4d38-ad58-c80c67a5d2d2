import React from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import EmployeeDashboard from '../features/dashboard/EmployeeDashboard.jsx'
import AdminDashboard from '../features/dashboard/AdminDashboard.jsx'
import ProtectedRoute from '../features/dashboard/components/ProtectedRoute.jsx'
import LeaveRequest from '../pages/LeaveRequest.jsx'
import Register from '../pages/Register.jsx'
import Login from '../pages/Login.jsx'

const AppRouter = () => {
    return (
        <BrowserRouter>
            <Routes>

                <Route
                    path="/admin-dashboard"
                    element={
                        <ProtectedRoute requireAdmin={true}>
                            <AdminDashboard />
                        </ProtectedRoute>
                    }
                />

                <Route
                    path="/"
                    element={
                        <ProtectedRoute>
                            <EmployeeDashboard />
                        </ProtectedRoute>
                    }
                />

                <Route path="/leave" element={<LeaveRequest />} />

                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                {/* <Route path="*" element={<Navigate to="/login" replace />} /> */}

            </Routes>
        </BrowserRouter>
    )
}

export default AppRouter

