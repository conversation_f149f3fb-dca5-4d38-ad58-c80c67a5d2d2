import React, { useState, useEffect } from "react";
import axios from "axios";
import Navbar from '../features/auth/components/Navbar';
import Sidebar from '../features/auth/components/Sidebar';

const LeaveDashboard = () => {
    const [leaves, setLeaves] = useState([]);
    const [loading, setLoading] = useState(true);

    // Fetch leaves from API
    useEffect(() => {
        const fetchLeaves = async () => {
            try {
                const response = await axios.get('http://localhost:3000/api/leave/my-leaves', {
                    withCredentials: true
                });
                // console.log('API Response:', response.data); // Debug log
                const leavesData = response.data.data?.leaves || response.data.data || [];
                setLeaves(Array.isArray(leavesData) ? leavesData : []);
            } catch (error) {
                console.error('Error fetching leaves:', error);
                setLeaves([]); // Set empty array on error
            } finally {
                setLoading(false);
            }
        };
        fetchLeaves();
    }, []);

    return (
        <div className="min-h-screen grid grid-cols-[240px_1fr] grid-rows-[auto_1fr]">
            {/* Navbar spans full width */}
            <div className="col-span-2">
                <Navbar />
            </div>
            
            {/* Sidebar */}
            <Sidebar role="employee" />
            
            {/* Main Content */}
            <div className="p-6 bg-gray-100 overflow-auto">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold">Manage Leaves</h1>
                    <button className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-md">
                        Add Leave
                    </button>
                </div>

                {/* Table */}
                <div className="overflow-x-auto bg-white rounded-lg shadow-md">
                    <table className="w-full text-sm text-left border-collapse">
                        <thead className="bg-gray-200 text-gray-700">
                            <tr>
                                <th className="px-4 py-3">SNO</th>
                                <th className="px-4 py-3">Leave Type</th>
                                <th className="px-4 py-3">From</th>
                                <th className="px-4 py-3">To</th>
                                <th className="px-4 py-3">Description</th>
                                <th className="px-4 py-3">Applied Date</th>
                                <th className="px-4 py-3">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                <tr>
                                    <td colSpan="7" className="text-center py-6 text-gray-500">
                                        Loading...
                                    </td>
                                </tr>
                            ) : Array.isArray(leaves) && leaves.length > 0 ? (
                                leaves.map((leave, index) => (
                                    <tr
                                        key={leave.id || index}
                                        className="border-t hover:bg-gray-50 transition"
                                    >
                                        <td className="px-4 py-3">{index + 1}</td>
                                        <td className="px-4 py-3">{leave.leaveType || 'N/A'}</td>
                                        <td className="px-4 py-3">{leave.startDate ? new Date(leave.startDate).toLocaleDateString() : 'N/A'}</td>
                                        <td className="px-4 py-3">{leave.endDate ? new Date(leave.endDate).toLocaleDateString() : 'N/A'}</td>
                                        <td className="px-4 py-3">{leave.reason || 'N/A'}</td>
                                        <td className="px-4 py-3">{leave.createdAt ? new Date(leave.createdAt).toLocaleDateString() : 'N/A'}</td>
                                        <td
                                            className={`px-4 py-3 font-medium ${leave.status === "Approved"
                                                    ? "text-green-600"
                                                    : leave.status === "Rejected"
                                                        ? "text-red-600"
                                                        : "text-yellow-600"
                                                }`}
                                        >
                                            {leave.status || 'Pending'}
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="7" className="text-center py-6 text-gray-500">
                                        No leaves found.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default LeaveDashboard;