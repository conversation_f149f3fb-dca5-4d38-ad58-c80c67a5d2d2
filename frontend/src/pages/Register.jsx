import React, { useState } from "react";
import axios from "axios";
import Input from "../shared/components/Input";
import Button from "../shared/components/Button";
import { Link, useNavigate } from 'react-router-dom'

const Register = () => {
    const [username, setUsername] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    // Validate inputs
    const validateForm = () => {
        const newErrors = {};
        if (!username.trim()) newErrors.username = "Username is required";
        if (!email.trim()) newErrors.email = "Email is required";
        if (!password.trim()) newErrors.password = "Password is required";
        return newErrors;
    };

    // Handle form submit
    const handleSubmit = async (e) => {
        e.preventDefault();

        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setErrors({});
        setLoading(true);

        try {
            // Change API endpoint to match your backend
            const response = await axios.post("http://localhost:3000/api/auth/register", {
                username,
                email,
                password,
            },
                {
                    withCredentials: true,
                }
            );

            console.log("Register Successful",);
            // console.log("Register Successful:", response.data);
            setEmail("");
            setPassword("");
            setUsername("");

            const { id, isAdmin, role } = response.data.user;
            localStorage.setItem("userId", id);
            localStorage.setItem("isAdmin", isAdmin);
            localStorage.setItem("role", role);

            navigate("/");

        } catch (error) {
            console.error("Register Failed:", error.response?.data || error.message);
            setErrors(error.response?.data?.message || "Registration Failed ❌");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
            <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8">
                <h1 className="text-center text-2xl font-semibold text-gray-800 mb-6">
                    Register - EMS
                </h1>

                <form className="space-y-4" onSubmit={handleSubmit}>
                    <Input
                        label="Username"
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="Enter Username"
                        error={errors.username}
                    />

                    <Input
                        label="Email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter Email"
                        error={errors.email}
                    />

                    <Input
                        label="Password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter Password"
                        error={errors.password}
                    />

                    <Button type="submit" disabled={loading}>
                        {loading ? "Registering..." : "Register"}
                    </Button>
                </form>
                <p className="text-center mt-4">
                    Already have an account? <Link to="/login" className="text-teal-600 hover:underline">Login</Link>
                </p>
            </div>
        </div>
    );
};

export default Register;
