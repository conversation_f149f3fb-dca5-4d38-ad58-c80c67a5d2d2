import React from "react";

const Button = ({ children, onClick, type = "button", disabled }) => {
    return (
        <button
            type={type}
            onClick={onClick}
            disabled={disabled}
            className={`w-full py-2 px-4 rounded-lg text-white transition duration-200 active:scale-[0.95] ${disabled
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-teal-600 hover:bg-teal-700"
                }`}
        >
            {children}
        </button>
    );
};

export default Button;
