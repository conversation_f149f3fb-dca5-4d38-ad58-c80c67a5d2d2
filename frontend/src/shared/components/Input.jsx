import React from "react";

const Input = ({ label, type = "text", value, onChange, placeholder, error }) => {
    return (
        <div className="mb-4">
            {label && (
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label}
                </label>
            )}
            <input
                type={type}
                value={value}
                onChange={onChange}
                placeholder={placeholder}
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 sm:text-sm ${error
                        ? "border-red-500 focus:ring-red-500"
                        : "border-gray-300 focus:ring-teal-500 focus:border-teal-500"
                    }`}
            />
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
        </div>
    );
};

export default Input;
