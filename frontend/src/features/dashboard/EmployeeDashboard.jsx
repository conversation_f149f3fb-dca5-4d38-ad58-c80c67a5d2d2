import React, { useEffect, useState } from "react";
import LeaveDetails from "../dashboard/components/LeaveDetails";
import Navbar from "../auth/components/Navbar";
import Sidebar from "../auth/components/Sidebar";
import { getUser } from "../../utils/getUser";

const EmployeeDashboard = () => {
    const [user, setUser] = useState(null);


    useEffect(() => {
        const fetchUser = async () => {
            const userData = await getUser();
            // console.log(userData);
            setUser(userData);
        };
        fetchUser();
    }, []); 

    return (
        <div className="min-h-screen grid grid-cols-[240px_1fr] grid-rows-[auto_1fr]">
            {/* Navbar */}
            <div className="col-span-2">
                <Navbar />
            </div>

            {/* Sidebar */}
            <Sidebar role="employee" />

            {/* Main content */}
            <div className="p-6 overflow-auto space-y-6">
                {/* Employee Details */}
                <div className="bg-white shadow-lg rounded-md p-6 w-full max-w-2xl">
                    <h2 className="text-xl font-bold mb-4 text-center">
                        Employee Details
                    </h2>
                    {user ? (
                        <div className="space-y-3 text-gray-700">
                            <p>
                                <span className="font-semibold">Name:</span>{" "}
                                {user.data.firstName} {user.data.lastName}
                            </p>
                            <p>
                                <span className="font-semibold">Employee ID:</span>{" "}
                                {user.data.id}
                            </p>
                            <p>
                                <span className="font-semibold">Username:</span>{" "}
                                {user.data.user?.username}
                            </p>
                            <p>
                                <span className="font-semibold">Email:</span>{" "}
                                {user.data.user?.email}
                            </p>
                            <p>
                                <span className="font-semibold">Department:</span>{" "}
                                {user.data.department || "N/A"}
                            </p>
                            <p>
                                <span className="font-semibold">Joining Date:</span>{" "}
                                {user.data.joiningDate
                                    ? new Date(user.data.joiningDate).toLocaleDateString()
                                    : "N/A"}
                            </p>
                            <p>
                                <span className="font-semibold">Role:</span>{" "}
                                {user.data.role || "N/A"}
                            </p>
                            <p>
                                <span className="font-semibold">Base Salary:</span>{" "}
                                ₹{user.data.salary?.base || "N/A"}
                            </p>
                            <p>
                                <span className="font-semibold">Allowance:</span>{" "}
                                ₹{user.data.salary?.allowance || "N/A"}
                            </p>
                            <p>
                                <span className="font-semibold">Deductions:</span>{" "}
                                ₹{user.data.salary?.deductions || "N/A"}
                            </p>
                        </div>
                    ) : (
                        <p className="text-gray-600">Loading employee details...</p>
                    )}
                </div>

                {/* Leave Details (optional) */}
                {/* <div>
                    <h2 className="text-xl font-bold mb-4">Leave Details</h2>
                    <LeaveDetails />
                </div> */}
            </div>
        </div>
    );
};

export default EmployeeDashboard;
