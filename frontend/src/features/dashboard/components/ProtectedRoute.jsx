import React from "react";
import { Navigate } from "react-router-dom";

const ProtectedRoute = ({ children, requireAdmin = false }) => {

    const isAdmin = localStorage.getItem("isAdmin") === "true";
    const userRole = localStorage.getItem("role");
    const userId = localStorage.getItem("userId");

    if (!userId) {
        // No user found → must login
        return <Navigate to="/login" replace />;
    }

    // If admin route required
    if (requireAdmin) {
        if (userRole === "admin" && isAdmin) {
            return children; // ✅ allow admin
        } else {
            return <Navigate to="/" replace />;
        }
    }

    // Normal employee access - allow all authenticated users for non-admin routes
    return children;
};

export default ProtectedRoute;