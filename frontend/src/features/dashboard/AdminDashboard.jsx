import React from "react";
import DashboardOverview from "../dashboard/components/DashboardOverview";
import LeaveDetails from "../dashboard/components/LeaveDetails";
import Navbar from "../auth/components/Navbar";
import Sidebar from "../auth/components/Sidebar";

const AdminDashboard = () => {
  return (
    <div className="min-h-screen grid grid-cols-[240px_1fr] grid-rows-[auto_1fr]">
      {/* Navbar spans full width */}
      <div className="col-span-2">
        <Navbar role="admin" />
      </div>
      
      {/* Sidebar */}
      <Sidebar role="admin" />
      
      {/* Main content area */}
      <div className="p-6 overflow-auto">
        <h2 className="text-xl font-bold mb-4">Dashboard Overview</h2>
        <DashboardOverview />
        <h2 className="text-xl font-bold mt-6 mb-4">Leave Details</h2>
        <LeaveDetails />
      </div>
    </div>
  );
};

export default AdminDashboard;