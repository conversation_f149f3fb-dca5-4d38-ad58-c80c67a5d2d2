import React, { useEffect, useState } from "react";
import axios from "axios";
import { useNavigate } from 'react-router-dom'
import { getUser } from "../../../utils/getUser";

const Navbar = ({ role }) => {
    const navigate = useNavigate();
    const [ user, setUser ] = useState(null);

    const handleLogout = () => {

        const userId = localStorage.getItem("userId");

        axios.post("http://localhost:3000/api/auth/logout", {
            userId
        }, {
            withCredentials: true,
        })
            .then(() => {
                localStorage.removeItem("userId");
                localStorage.removeItem("isAdmin");
                localStorage.removeItem("role");
                navigate("/login");
            })
            .catch((error) => {
                console.error("Logout failed:", error);
            });
    }

    useEffect(() => {
        const fetchUser = async () => {
            console.log(await getUser())
            setUser(await getUser());
        };
        fetchUser();
    }, []);

    return (
        <div className="flex justify-between items-center bg-teal-600 text-white p-4">
            <h1 className="text-xl font-bold">Employee MS</h1>
            <div className="flex items-center gap-4">
                <span>Welcome, {user?.data.firstName}</span>
                <button className="bg-red-500 hover:bg-red-600 px-3 py-1 rounded" onClick={handleLogout}>
                    Logout
                </button>
            </div>
        </div>
    );
};

export default Navbar;
