import React from "react";
import { useNavigate } from "react-router-dom";

const Sidebar = ({ role }) => {
  const navigate = useNavigate();

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <div className="bg-gray-800 text-white w-60 min-h-screen p-4">
      <ul className="space-y-3">
        <li 
          className="hover:bg-gray-700 p-2 rounded cursor-pointer"
          onClick={() => handleNavigation('/')}
        >
          Dashboard
        </li>
        {role === "admin" && (
          <>
            <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Employees</li>
            <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Departments</li>
            <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Salary</li>
            <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Setting</li>
          </>
        )}
        <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Attendance</li>
        <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Departments</li>
        <li className="hover:bg-gray-700 p-2 rounded cursor-pointer">Salary</li>
        <li 
          className="hover:bg-gray-700 p-2 rounded cursor-pointer"
          onClick={() => handleNavigation('/leave')}
        >
          Leaves
        </li>
      </ul>
    </div>
  );
};

export default Sidebar;